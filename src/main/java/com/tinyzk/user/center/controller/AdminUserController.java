package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.annotation.DataPermission;
import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import com.tinyzk.user.center.service.AdminUserService;
import com.tinyzk.user.center.service.BatchResumeParseService;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import com.tinyzk.user.center.vo.PageResponseVO;
import com.tinyzk.user.center.vo.UserDetailVO;
import com.tinyzk.user.center.vo.UserListVO;

import com.tinyzk.user.center.dto.DisableUserRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import org.springframework.http.MediaType;
import javax.validation.constraints.Positive;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理员用户管理控制器
 * 使用管理端权限验证模式，仅验证客户端管理员权限
 */
@Tag(name = "管理员用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/users")
@Slf4j
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private BatchResumeParseService batchResumeParseService;

    /**
     * 获取用户列表
     *
     * @param pageNumber 页码
     * @param pageSize 每页数量
     * @param userId 用户ID
     * @param nickname 用户昵称
     * @param identifier 登录标识
     * @param status 用户状态
     * @param realNameVerified 实名认证状态
     * @param registeredAfter 注册时间晚于
     * @param registeredBefore 注册时间早于
     * @param lastLoginAfter 最后登录时间晚于
     * @param lastLoginBefore 最后登录时间早于
     * @param sortBy 排序字段
     * @param sortOrder 排序顺序
     * @return 用户列表分页数据
     */
    @GetMapping
    @DataPermission(mode = DataPermission.Mode.ADMIN)  // 管理端模式
    @Operation(summary = "获取用户列表", description = "获取用户列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "需要管理员权限"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<PageResponseVO<UserListVO>> getUserList(@Validated @Valid @ModelAttribute UserListRequestDTO requestDTO) {
        PageResponseVO<UserListVO> pageResponse = adminUserService.getUserList(requestDTO);
        return Result.success(pageResponse);
    }
    
    /**
     * 获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情信息
     */
    @GetMapping("/{userId}")
    @DataPermission(mode = DataPermission.Mode.ADMIN)  // 管理端模式
    @Operation(summary = "获取用户详情", description = "获取用户详情")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "需要管理员权限"),
        @ApiResponse(responseCode = "404", description = "用户不存在"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserDetailVO> getUserDetail(@PathVariable("userId") @Validated @Positive Long userId) {
        UserDetailVO userDetail = adminUserService.getUserDetail(userId);
        if (userDetail == null) {
            return Result.error("用户不存在", 404);
        }
        return Result.success(userDetail);
    }
    
    // /**
    //  * 更新用户信息
    //  *
    //  * @param userId 用户ID
    //  * @param requestDTO 更新用户信息请求DTO
    //  * @return 更新结果
    //  */


    // @Operation(summary = "更新用户信息", description = "更新用户信息")
    // @PutMapping("/{userId}")
    // public Result<String> updateUser(@PathVariable("userId") @Validated @Positive Long userId,
    //         @RequestBody UpdateUserRequestDTO requestDTO
    // ) {
    //     boolean success = adminUserService.updateUser(userId, requestDTO);
    //     if (!success) {
    //         return Result.error("用户不存在或更新失败", 404);
    //     }
    //     return Result.success("更新成功");
    // }
    
    /**
     * 禁用用户
     *
     * @param userId 用户ID
     * @param request 禁用请求
     * @return 操作结果
     */
    @PostMapping("/{userId}/disable")
    @DataPermission(mode = DataPermission.Mode.ADMIN)  // 管理端模式
    @Operation(
        summary = "禁用用户",
        description = "禁用指定用户账户，被禁用的用户将无法登录系统"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "禁用成功"),
        @ApiResponse(responseCode = "400", description = "用户不存在或禁用失败"),
        @ApiResponse(responseCode = "403", description = "需要管理员权限"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "禁用原因",
        required = false,
        content = @io.swagger.v3.oas.annotations.media.Content(
            mediaType = "application/json",
            schema = @Schema(implementation = DisableUserRequest.class)
        )
    )
    public Result<Void> disableUser(
            @Parameter(description = "用户ID", required = true, example = "123")
            @PathVariable("userId") @Validated @Positive Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(required = false) 
            @RequestBody(required = false) DisableUserRequest request) {
        String reason = request != null ? request.getReason() : null;
        boolean success = adminUserService.disableUser(userId, reason);
        if (!success) {
            return Result.error("用户不存在或禁用失败", 400);
        }
        return Result.success();
    }
    
    /**
     * 启用用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/{userId}/enable")
    @DataPermission(mode = DataPermission.Mode.ADMIN)  // 管理端模式
    @Operation(
        summary = "启用用户",
        description = "启用指定用户账户，被启用的用户可以正常登录系统"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "启用成功"),
        @ApiResponse(responseCode = "400", description = "用户不存在或启用失败"),
        @ApiResponse(responseCode = "403", description = "需要管理员权限"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> enableUser(
            @Parameter(description = "用户ID", required = true, example = "123")
            @PathVariable("userId") @Validated @Positive Long userId) {
        boolean success = adminUserService.enableUser(userId);
        if (!success) {
            return Result.error("用户不存在或启用失败", 400);
        }
        return Result.success();
    }

    /**
     * 批量上传并解析简历文件
     *
     * @param files 简历文件数组
     * @param overwriteExisting 是否覆盖现有数据
     * @param parseBasicInfo 是否解析基本信息
     * @param parseContactInfo 是否解析联系方式
     * @param parseEducation 是否解析教育经历
     * @param parseWorkExperience 是否解析工作经历
     * @param parseProjectExperience 是否解析项目经历
     * @param parseSkills 是否解析技能信息
     * @param parseTraining 是否解析培训经历
     * @param parseLanguages 是否解析语言能力
     * @param parseCertificates 是否解析证书信息
     * @param parseAwards 是否解析获奖记录
     * @param maxConcurrency 最大并发处理数量
     * @param timeoutSeconds 单个文件处理超时时间（秒）
     * @return 批量处理结果
     */
    @PostMapping(value = "/batch-resume-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @DataPermission(mode = DataPermission.Mode.ADMIN)  // 管理端模式
    @Operation(summary = "批量上传并解析简历文件",
               description = "管理员批量上传简历文件(支持doc、docx、pdf格式)并调用第三方服务进行解析，自动创建用户账户")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "批量处理完成"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "403", description = "需要管理员权限"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<BatchResumeUploadResultVO> batchUploadResumes(
            @Parameter(description = "简历文件数组", required = true)
            @RequestParam("files") @NotNull MultipartFile[] files,

            @Parameter(description = "是否覆盖现有数据", example = "false")
            @RequestParam(value = "overwriteExisting", defaultValue = "false") Boolean overwriteExisting,

            @Parameter(description = "是否解析基本信息", example = "true")
            @RequestParam(value = "parseBasicInfo", defaultValue = "true") Boolean parseBasicInfo,

            @Parameter(description = "是否解析联系方式", example = "true")
            @RequestParam(value = "parseContactInfo", defaultValue = "true") Boolean parseContactInfo,

            @Parameter(description = "是否解析教育经历", example = "true")
            @RequestParam(value = "parseEducation", defaultValue = "true") Boolean parseEducation,

            @Parameter(description = "是否解析工作经历", example = "true")
            @RequestParam(value = "parseWorkExperience", defaultValue = "true") Boolean parseWorkExperience,

            @Parameter(description = "是否解析项目经历", example = "true")
            @RequestParam(value = "parseProjectExperience", defaultValue = "true") Boolean parseProjectExperience,

            @Parameter(description = "是否解析技能信息", example = "true")
            @RequestParam(value = "parseSkills", defaultValue = "true") Boolean parseSkills,

            @Parameter(description = "是否解析培训经历", example = "true")
            @RequestParam(value = "parseTraining", defaultValue = "true") Boolean parseTraining,

            @Parameter(description = "是否解析语言能力", example = "true")
            @RequestParam(value = "parseLanguages", defaultValue = "true") Boolean parseLanguages,

            @Parameter(description = "是否解析证书信息", example = "true")
            @RequestParam(value = "parseCertificates", defaultValue = "true") Boolean parseCertificates,

            @Parameter(description = "是否解析获奖记录", example = "true")
            @RequestParam(value = "parseAwards", defaultValue = "true") Boolean parseAwards,

            @Parameter(description = "最大并发处理数量", example = "5")
            @RequestParam(value = "maxConcurrency", defaultValue = "5") Integer maxConcurrency,

            @Parameter(description = "单个文件处理超时时间（秒）", example = "60")
            @RequestParam(value = "timeoutSeconds", defaultValue = "60") Integer timeoutSeconds) {

        log.info("管理员批量上传简历文件: fileCount={}, overwriteExisting={}",
                files.length, overwriteExisting);

        // 构建请求DTO
        BatchResumeUploadRequestDTO requestDTO = new BatchResumeUploadRequestDTO();
        requestDTO.setFiles(files);
        requestDTO.setOverwriteExisting(overwriteExisting);
        requestDTO.setParseBasicInfo(parseBasicInfo);
        requestDTO.setParseContactInfo(parseContactInfo);
        requestDTO.setParseEducation(parseEducation);
        requestDTO.setParseWorkExperience(parseWorkExperience);
        requestDTO.setParseProjectExperience(parseProjectExperience);
        requestDTO.setParseSkills(parseSkills);
        requestDTO.setParseTraining(parseTraining);
        requestDTO.setParseLanguages(parseLanguages);
        requestDTO.setParseCertificates(parseCertificates);
        requestDTO.setParseAwards(parseAwards);
        requestDTO.setMaxConcurrency(maxConcurrency);
        requestDTO.setTimeoutSeconds(timeoutSeconds);

        BatchResumeUploadResultVO result = batchResumeParseService.batchParseResumes(requestDTO);
        return Result.success(result, "批量简历解析完成");
    }
}
