package com.tinyzk.user.center.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 批量处理进度跟踪器
 */
@Data
@Slf4j
public class BatchProcessProgressTracker {

    private final String batchId;
    private final int totalFiles;
    private final LocalDateTime startTime;
    
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicInteger skippedCount = new AtomicInteger(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);

    public BatchProcessProgressTracker(String batchId, int totalFiles) {
        this.batchId = batchId;
        this.totalFiles = totalFiles;
        this.startTime = LocalDateTime.now();
        log.info("批量处理开始: batchId={}, totalFiles={}", batchId, totalFiles);
    }

    /**
     * 记录文件处理完成
     */
    public void recordFileProcessed(String fileName, boolean success, boolean skipped, long processingTimeMs) {
        int processed = processedCount.incrementAndGet();
        totalProcessingTime.addAndGet(processingTimeMs);
        
        if (skipped) {
            skippedCount.incrementAndGet();
        } else if (success) {
            successCount.incrementAndGet();
        } else {
            failureCount.incrementAndGet();
        }
        
        // 每处理10个文件或处理完成时记录进度
        if (processed % 10 == 0 || processed == totalFiles) {
            logProgress(fileName);
        }
    }

    /**
     * 记录处理进度
     */
    private void logProgress(String currentFileName) {
        double progressPercent = (double) processedCount.get() / totalFiles * 100;
        double avgProcessingTime = processedCount.get() > 0 ? 
            (double) totalProcessingTime.get() / processedCount.get() : 0;
        
        log.info("批量处理进度: batchId={}, 进度={}/{} ({:.1f}%), 成功={}, 失败={}, 跳过={}, 平均耗时={:.1f}ms, 当前文件={}",
                batchId, processedCount.get(), totalFiles, progressPercent,
                successCount.get(), failureCount.get(), skippedCount.get(),
                avgProcessingTime, currentFileName);
    }

    /**
     * 获取当前进度百分比
     */
    public double getProgressPercent() {
        return (double) processedCount.get() / totalFiles * 100;
    }

    /**
     * 获取平均处理时间
     */
    public double getAverageProcessingTime() {
        return processedCount.get() > 0 ? 
            (double) totalProcessingTime.get() / processedCount.get() : 0;
    }

    /**
     * 估算剩余时间（毫秒）
     */
    public long getEstimatedRemainingTime() {
        if (processedCount.get() == 0) {
            return 0;
        }
        
        double avgTime = getAverageProcessingTime();
        int remainingFiles = totalFiles - processedCount.get();
        return (long) (avgTime * remainingFiles);
    }

    /**
     * 记录批量处理完成
     */
    public void recordBatchCompleted() {
        long totalDuration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
        
        log.info("批量处理完成: batchId={}, 总耗时={}ms, 总文件数={}, 成功={}, 失败={}, 跳过={}, 成功率={:.1f}%",
                batchId, totalDuration, totalFiles, 
                successCount.get(), failureCount.get(), skippedCount.get(),
                totalFiles > 0 ? (double) successCount.get() / totalFiles * 100 : 0);
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return processedCount.get() >= totalFiles;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return processedCount.get() > 0 ? 
            (double) successCount.get() / processedCount.get() * 100 : 0;
    }
}
