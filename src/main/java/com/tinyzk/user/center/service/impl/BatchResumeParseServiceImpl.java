package com.tinyzk.user.center.service.impl;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.dto.UserDuplicationCheckDTO;
import com.tinyzk.user.center.entity.UserAuth;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import com.tinyzk.user.center.entity.UserContactMethods;
import com.tinyzk.user.center.entity.UserEducationHistory;
import com.tinyzk.user.center.entity.UserProjectHistory;
import com.tinyzk.user.center.entity.UserSkills;
import com.tinyzk.user.center.entity.UserWorkHistory;
import com.tinyzk.user.center.entity.UserAwards;
import com.tinyzk.user.center.entity.UserTraining;
import com.tinyzk.user.center.mapper.AdminUserMapper;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserContactMethodsMapper;
import com.tinyzk.user.center.mapper.UserEducationHistoryMapper;
import com.tinyzk.user.center.mapper.UserProjectHistoryMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.mapper.UserSkillsMapper;
import com.tinyzk.user.center.mapper.UserWorkHistoryMapper;
import com.tinyzk.user.center.mapper.UserAwardsMapper;
import com.tinyzk.user.center.mapper.UserTrainingMapper;
import com.tinyzk.user.center.service.BatchResumeParseService;
import com.tinyzk.user.center.service.ResumeDataConversionService;
import com.tinyzk.user.center.service.ThirdPartyResumeParseService;
import com.tinyzk.user.center.util.BatchProcessProgressTracker;
import com.tinyzk.user.center.util.FileValidationUtil;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tinyzk.user.center.common.BatchResumeParseConstants;
import com.tinyzk.user.center.common.exception.ResumeParseException;

/**
 * 批量简历解析服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BatchResumeParseServiceImpl implements BatchResumeParseService {

    private final ThirdPartyResumeParseService thirdPartyResumeParseService;
    private final ResumeDataConversionService conversionService;
    private final AdminUserMapper adminUserMapper;
    private final UserBaseMapper userBaseMapper;
    private final UserAuthMapper userAuthMapper;
    private final UserProfileMapper userProfileMapper;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final UserContactMethodsMapper contactMethodsMapper;
    private final UserEducationHistoryMapper educationMapper;
    private final UserWorkHistoryMapper workHistoryMapper;
    private final UserProjectHistoryMapper projectHistoryMapper;
    private final UserSkillsMapper skillsMapper;
    private final UserAwardsMapper awardsMapper;
    private final UserTrainingMapper trainingMapper;
    private final FileValidationUtil fileValidationUtil;
    private final ObjectMapper objectMapper;

    @Qualifier("batchResumeParseExecutor")
    private final Executor batchResumeParseExecutor;



    @Override
    public BatchResumeUploadResultVO batchParseResumes(BatchResumeUploadRequestDTO requestDTO) {
        LocalDateTime startTime = LocalDateTime.now();
        String batchId = "batch_" + System.currentTimeMillis();
        log.info("开始批量解析简历，batchId={}, 文件数量: {}", batchId, requestDTO.getFiles().length);

        // 验证文件
        try {
            fileValidationUtil.validateBatchFiles(requestDTO.getFiles());
            fileValidationUtil.validateConcurrencyParams(requestDTO.getMaxConcurrency(), requestDTO.getTimeoutSeconds());
            log.info("文件验证通过，batchId={}", batchId);
        } catch (Exception e) {
            log.error("文件验证失败，batchId={}", batchId, e);
            throw e;
        }

        // 创建进度跟踪器
        BatchProcessProgressTracker progressTracker = new BatchProcessProgressTracker(batchId, requestDTO.getFiles().length);

        // 初始化结果对象
        BatchResumeUploadResultVO result = new BatchResumeUploadResultVO();
        result.setStartTime(startTime);

        // 初始化统计信息
        BatchResumeUploadResultVO.ProcessStatistics statistics = new BatchResumeUploadResultVO.ProcessStatistics();
        statistics.setTotalFiles(requestDTO.getFiles().length);

        // 初始化文件处理结果列表
        List<BatchResumeUploadResultVO.FileProcessResult> fileResults = new ArrayList<>();

        // 原子计数器
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);
        AtomicInteger newUserCount = new AtomicInteger(0);
        AtomicInteger updatedUserCount = new AtomicInteger(0);

        // 创建异步任务列表，使用配置的线程池
        List<CompletableFuture<BatchResumeUploadResultVO.FileProcessResult>> futures = new ArrayList<>();

        for (MultipartFile file : requestDTO.getFiles()) {
            CompletableFuture<BatchResumeUploadResultVO.FileProcessResult> future =
                CompletableFuture.supplyAsync(() -> processFile(file, requestDTO, progressTracker), batchResumeParseExecutor)
                    .orTimeout(requestDTO.getTimeoutSeconds(), TimeUnit.SECONDS)
                    .exceptionally(throwable -> {
                        log.error("处理文件 {} 时发生异常", file.getOriginalFilename(), throwable);
                        BatchResumeUploadResultVO.FileProcessResult errorResult = createErrorResult(file, throwable);
                        // 更新进度跟踪器
                        progressTracker.recordFileProcessed(file.getOriginalFilename(), false, false,
                            errorResult.getProcessingTimeMs() != null ? errorResult.getProcessingTimeMs() : 0);
                        return errorResult;
                    });
            futures.add(future);
        }
            
        // 等待所有任务完成
        for (CompletableFuture<BatchResumeUploadResultVO.FileProcessResult> future : futures) {
            try {
                BatchResumeUploadResultVO.FileProcessResult fileResult = future.get();
                fileResults.add(fileResult);

                // 更新统计信息
                switch (fileResult.getStatus()) {
                    case SUCCESS:
                        successCount.incrementAndGet();
                        if (fileResult.getUserOperation() == BatchResumeUploadResultVO.UserOperationType.CREATED) {
                            newUserCount.incrementAndGet();
                        } else if (fileResult.getUserOperation() == BatchResumeUploadResultVO.UserOperationType.UPDATED) {
                            updatedUserCount.incrementAndGet();
                        }
                        break;
                    case FAILURE:
                        failureCount.incrementAndGet();
                        break;
                    case SKIPPED:
                        skippedCount.incrementAndGet();
                        break;
                }
            } catch (Exception e) {
                log.error("获取文件处理结果时发生异常", e);
                failureCount.incrementAndGet();
            }
        }
        
        // 完善统计信息
        statistics.setSuccessCount(successCount.get());
        statistics.setFailureCount(failureCount.get());
        statistics.setSkippedCount(skippedCount.get());
        statistics.setNewUserCount(newUserCount.get());
        statistics.setUpdatedUserCount(updatedUserCount.get());
        statistics.setSuccessRate((double) successCount.get() / statistics.getTotalFiles());
        
        // 设置结果
        LocalDateTime endTime = LocalDateTime.now();
        result.setEndTime(endTime);
        result.setTotalDurationMs(java.time.Duration.between(startTime, endTime).toMillis());
        result.setStatistics(statistics);
        result.setFileResults(fileResults);
        
        // 记录批量处理完成
        progressTracker.recordBatchCompleted();

        log.info("批量解析简历完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
                statistics.getTotalFiles(), statistics.getSuccessCount(),
                statistics.getFailureCount(), statistics.getSkippedCount(),
                result.getTotalDurationMs());

        return result;
    }

    /**
     * 处理单个文件
     */
    private BatchResumeUploadResultVO.FileProcessResult processFile(MultipartFile file, BatchResumeUploadRequestDTO requestDTO, BatchProcessProgressTracker progressTracker) {
        long startTime = System.currentTimeMillis();
        BatchResumeUploadResultVO.FileProcessResult fileResult = new BatchResumeUploadResultVO.FileProcessResult();
        fileResult.setFileName(file.getOriginalFilename());
        fileResult.setFileSize(file.getSize());

        try {
            log.info("开始处理文件: {}", file.getOriginalFilename());

            // 调用第三方服务解析简历
            ThirdPartyParseResultDTO parseResult = thirdPartyResumeParseService.parseResume(file);

            // 从解析结果中提取用户信息
            BatchResumeUploadResultVO.UserInfoSummary userInfo = extractUserInfo(parseResult);
            fileResult.setUserInfo(userInfo);

            // 检查用户是否重复
            UserDuplicationCheckDTO.CheckResult duplicationResult = checkUserDuplication(userInfo);

            Long finalUserId;
            Long parseRecordId = null;
            BatchResumeUploadResultVO.UserOperationType userOperation;

            if (duplicationResult.getIsDuplicate()) {
                // 用户已存在
                finalUserId = duplicationResult.getDuplicateUserId();

                if (requestDTO.getOverwriteExisting()) {
                    // 更新现有用户信息
                    updateExistingUser(finalUserId, parseResult, requestDTO, file);
                    userOperation = BatchResumeUploadResultVO.UserOperationType.UPDATED;
                    fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
                    // 为更新的用户保存解析记录
                    parseRecordId = saveParseRecord(finalUserId, parseResult, file);
                } else {
                    // 跳过重复用户，但仍然保存解析记录用于审计
                    userOperation = BatchResumeUploadResultVO.UserOperationType.SKIPPED;
                    fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SKIPPED);
                    parseRecordId = saveParseRecord(finalUserId, parseResult, file);
                }
            } else {
                // 创建新用户，createNewUser方法会返回解析记录ID
                CreateUserResult createResult = createNewUserWithParseRecord(parseResult, requestDTO, file);
                finalUserId = createResult.getUserId();
                parseRecordId = createResult.getParseRecordId();
                userOperation = BatchResumeUploadResultVO.UserOperationType.CREATED;
                fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
            }

            fileResult.setUserId(finalUserId);
            fileResult.setUserOperation(userOperation);
            fileResult.setParseRecordId(parseRecordId);

        } catch (Exception e) {
            log.error("处理文件 {} 失败", file.getOriginalFilename(), e);
            fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.FAILURE);
            fileResult.setErrorMessage(e.getMessage());
            fileResult.setErrorType(determineErrorType(e));
        }

        long processingTime = System.currentTimeMillis() - startTime;
        fileResult.setProcessingTimeMs(processingTime);

        // 更新进度跟踪器
        boolean success = fileResult.getStatus() == BatchResumeUploadResultVO.ProcessStatus.SUCCESS;
        boolean skipped = fileResult.getStatus() == BatchResumeUploadResultVO.ProcessStatus.SKIPPED;
        progressTracker.recordFileProcessed(file.getOriginalFilename(), success, skipped, processingTime);

        return fileResult;
    }

    /**
     * 从第三方解析结果中提取用户信息
     */
    private BatchResumeUploadResultVO.UserInfoSummary extractUserInfo(ThirdPartyParseResultDTO parseResult) {
        BatchResumeUploadResultVO.UserInfoSummary userInfo = new BatchResumeUploadResultVO.UserInfoSummary();

        if (parseResult.getParsingResult() != null) {
            ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();

            // 提取基本信息
            if (result.getBasicInfo() != null) {
                ThirdPartyParseResultDTO.BasicInfo basicInfo = result.getBasicInfo();
                userInfo.setName(basicInfo.getName());
                userInfo.setGender(convertGender(basicInfo.getGender()));
                userInfo.setAge(basicInfo.getAge());

                // 计算工作年限（简单估算）
                if (basicInfo.getAge() != null && basicInfo.getAge() > 22) {
                    userInfo.setWorkExperience((basicInfo.getAge() - 22) + "年");
                }

                userInfo.setEducation(basicInfo.getDegree());
            }

            // 提取联系方式
            if (result.getContactInfo() != null) {
                ThirdPartyParseResultDTO.ContactInfo contactInfo = result.getContactInfo();
                userInfo.setPhone(contactInfo.getPhoneNumber());
                userInfo.setEmail(contactInfo.getEmail());
            }
        }

        return userInfo;
    }

    /**
     * 转换性别信息为字符串
     */
    private String convertGender(String gender) {
        if (gender == null) return "未知";
        switch (gender) {
            case BatchResumeParseConstants.Gender.MALE_TEXT:
                return BatchResumeParseConstants.Gender.MALE_TEXT;
            case BatchResumeParseConstants.Gender.FEMALE_TEXT:
                return BatchResumeParseConstants.Gender.FEMALE_TEXT;
            default:
                return "未知";
        }
    }

    /**
     * 转换性别信息为整数
     */
    private Integer convertGenderToInteger(String gender) {
        if (gender == null) return BatchResumeParseConstants.Gender.UNKNOWN;
        switch (gender) {
            case BatchResumeParseConstants.Gender.MALE_TEXT:
                return BatchResumeParseConstants.Gender.MALE;
            case BatchResumeParseConstants.Gender.FEMALE_TEXT:
                return BatchResumeParseConstants.Gender.FEMALE;
            default:
                return BatchResumeParseConstants.Gender.UNKNOWN;
        }
    }

    /**
     * 检查用户重复（重载方法）
     */
    private UserDuplicationCheckDTO.CheckResult checkUserDuplication(BatchResumeUploadResultVO.UserInfoSummary userInfo) {
        UserDuplicationCheckDTO checkDTO = new UserDuplicationCheckDTO();
        checkDTO.setPhone(userInfo.getPhone());
        checkDTO.setEmail(userInfo.getEmail());
        checkDTO.setRealName(userInfo.getName());

        return checkUserDuplication(checkDTO);
    }

    /**
     * 创建新用户并返回解析记录ID
     */
    private CreateUserResult createNewUserWithParseRecord(ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            // 创建用户基础信息
            UserBase userBase = new UserBase();
            userBase.setStatus(BatchResumeParseConstants.UserStatus.ACTIVE);
            userBaseMapper.insert(userBase);
            Long userId = userBase.getUserId();

            log.info("创建新用户成功: userId={}", userId);

            // 创建用户认证信息（如果有邮箱或手机号）
            if (parseResult.getParsingResult() != null && parseResult.getParsingResult().getContactInfo() != null) {
                createUserAuth(userId, parseResult.getParsingResult().getContactInfo());
            }

            // 创建用户资料
            createUserProfile(userId, parseResult);

            // 保存解析记录和详细信息
            Long parseRecordId = saveParseDataToDatabase(userId, parseResult, requestDTO, file);

            return new CreateUserResult(userId, parseRecordId);

        } catch (Exception e) {
            log.error(BatchResumeParseConstants.ErrorMessages.CREATE_USER_FAILED, e);
            throw new ResumeParseException(BatchResumeParseConstants.ErrorMessages.CREATE_USER_FAILED, e);
        }
    }



    /**
     * 更新现有用户
     */
    private void updateExistingUser(Long userId, ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            log.info("更新现有用户: userId={}", userId);

            // 更新用户资料
            updateUserProfile(userId, parseResult);

            // 更新解析数据（如果允许覆盖）
            if (requestDTO.getOverwriteExisting()) {
                saveParseDataToDatabase(userId, parseResult, requestDTO, file);
            }

            log.info("更新用户信息成功: userId={}", userId);

        } catch (Exception e) {
            log.error("更新用户失败: userId={}", userId, e);
            throw new RuntimeException("更新用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建错误结果
     */
    private BatchResumeUploadResultVO.FileProcessResult createErrorResult(MultipartFile file, Throwable throwable) {
        BatchResumeUploadResultVO.FileProcessResult result = new BatchResumeUploadResultVO.FileProcessResult();
        result.setFileName(file.getOriginalFilename());
        result.setFileSize(file.getSize());
        result.setStatus(BatchResumeUploadResultVO.ProcessStatus.FAILURE);
        result.setErrorMessage(throwable.getMessage());
        result.setErrorType(determineErrorType(throwable));
        return result;
    }

    /**
     * 确定错误类型
     */
    private BatchResumeUploadResultVO.ErrorType determineErrorType(Throwable throwable) {
        if (throwable instanceof java.util.concurrent.TimeoutException) {
            return BatchResumeUploadResultVO.ErrorType.TIMEOUT;
        }

        String message = throwable.getMessage();
        if (message != null) {
            message = message.toLowerCase();

            if (message.contains("文件") || message.contains("file") ||
                message.contains("格式") || message.contains("大小") ||
                message.contains("类型")) {
                return BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION;
            }

            if (message.contains("api") || message.contains("解析") ||
                message.contains("parse") || message.contains("第三方") ||
                message.contains("网络") || message.contains("连接")) {
                return BatchResumeUploadResultVO.ErrorType.PARSE_API_ERROR;
            }

            if (message.contains("数据库") || message.contains("database") ||
                message.contains("sql") || message.contains("insert") ||
                message.contains("update") || message.contains("constraint")) {
                return BatchResumeUploadResultVO.ErrorType.DATABASE_ERROR;
            }

            if (message.contains("转换") || message.contains("conversion") ||
                message.contains("mapping") || message.contains("数据")) {
                return BatchResumeUploadResultVO.ErrorType.DATA_CONVERSION;
            }
        }

        // 根据异常类型判断
        if (throwable instanceof java.sql.SQLException ||
            throwable instanceof org.springframework.dao.DataAccessException) {
            return BatchResumeUploadResultVO.ErrorType.DATABASE_ERROR;
        }

        if (throwable instanceof java.io.IOException ||
            throwable instanceof java.net.SocketTimeoutException) {
            return BatchResumeUploadResultVO.ErrorType.PARSE_API_ERROR;
        }

        if (throwable instanceof IllegalArgumentException ||
            throwable instanceof NumberFormatException) {
            return BatchResumeUploadResultVO.ErrorType.DATA_CONVERSION;
        }

        return BatchResumeUploadResultVO.ErrorType.UNKNOWN;
    }

    /**
     * 创建用户资料
     */
    private void createUserProfile(Long userId, ThirdPartyParseResultDTO parseResult) {
        if (parseResult.getParsingResult() == null) return;

        UserProfile userProfile = new UserProfile();
        userProfile.setUserId(userId);

        // 设置基本信息
        if (parseResult.getParsingResult().getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parseResult.getParsingResult().getBasicInfo();

            // 使用姓名作为昵称
            if (basicInfo.getName() != null) {
                userProfile.setNickname(basicInfo.getName());
            }

            // 设置性别
            if (basicInfo.getGender() != null) {
                userProfile.setGender(convertGenderToInteger(basicInfo.getGender()));
            }

            // 设置地区信息
            if (basicInfo.getCurrentLocation() != null) {
                userProfile.setRegionName(basicInfo.getCurrentLocation());
            }

            // 设置生日
            if (basicInfo.getDateOfBirth() != null) {
                try {
                    LocalDate birthday = parseBirthday(basicInfo.getDateOfBirth());
                    userProfile.setBirthday(birthday);
                    log.debug("设置用户生日: userId={}, birthday={}", userId, birthday);
                } catch (Exception e) {
                    log.warn("解析生日失败: userId={}, dateOfBirth={}", userId, basicInfo.getDateOfBirth(), e);
                }
            }
        }

        userProfileMapper.insert(userProfile);
        log.info("创建用户资料成功: userId={}", userId);
    }

    /**
     * 更新用户资料
     */
    private void updateUserProfile(Long userId, ThirdPartyParseResultDTO parseResult) {
        if (parseResult.getParsingResult() == null) return;

        // 查询现有用户资料
        UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
        if (existingProfile == null) {
            // 如果不存在，则创建
            createUserProfile(userId, parseResult);
            return;
        }

        // 更新基本信息
        if (parseResult.getParsingResult().getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parseResult.getParsingResult().getBasicInfo();

            // 更新昵称（如果原来没有）
            if (existingProfile.getNickname() == null && basicInfo.getName() != null) {
                existingProfile.setNickname(basicInfo.getName());
            }

            // 更新性别（如果原来没有）
            if (existingProfile.getGender() == null || existingProfile.getGender().equals(BatchResumeParseConstants.Gender.UNKNOWN)) {
                if (basicInfo.getGender() != null) {
                    existingProfile.setGender(convertGenderToInteger(basicInfo.getGender()));
                }
            }

            // 更新地区信息（如果原来没有）
            if (existingProfile.getRegionName() == null && basicInfo.getCurrentLocation() != null) {
                existingProfile.setRegionName(basicInfo.getCurrentLocation());
            }

            // 更新生日（如果原来没有）
            if (existingProfile.getBirthday() == null && basicInfo.getDateOfBirth() != null) {
                try {
                    LocalDate birthday = parseBirthday(basicInfo.getDateOfBirth());
                    existingProfile.setBirthday(birthday);
                    log.debug("更新用户生日: userId={}, birthday={}", userId, birthday);
                } catch (Exception e) {
                    log.warn("更新时解析生日失败: userId={}, dateOfBirth={}", userId, basicInfo.getDateOfBirth(), e);
                }
            }
        }

        userProfileMapper.updateById(existingProfile);
        log.info("更新用户资料成功: userId={}", userId);
    }

    @Override
    public UserDuplicationCheckDTO.CheckResult checkUserDuplication(UserDuplicationCheckDTO checkDTO) {
        UserDuplicationCheckDTO.CheckResult result = new UserDuplicationCheckDTO.CheckResult();
        result.setIsDuplicate(false);

        // 根据用户反馈，优先根据手机号和姓名进行去重
        if (checkDTO.getPhone() != null && checkDTO.getRealName() != null) {
            // 先尝试手机号+姓名匹配
            Long userId = adminUserMapper.selectUserIdByPhoneAndRealName(checkDTO.getPhone(), checkDTO.getRealName());
            if (userId != null) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userId);
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.PHONE);
                result.setMatchedIdentifier(checkDTO.getPhone());
                return result;
            }
        }

        // 如果手机号+姓名没有匹配，再尝试单独手机号匹配
        if (checkDTO.getPhone() != null) {
            Long userId = adminUserMapper.selectUserIdByPhone(checkDTO.getPhone());
            if (userId != null) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userId);
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.PHONE);
                result.setMatchedIdentifier(checkDTO.getPhone());
                return result;
            }
        }

        // 最后尝试姓名匹配（可能有多个同名用户，取第一个）
        if (checkDTO.getRealName() != null) {
            List<Long> userIds = adminUserMapper.selectUserIdsByRealName(checkDTO.getRealName());
            if (userIds != null && !userIds.isEmpty()) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userIds.get(0));
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.NAME_AND_ID_CARD);
                result.setMatchedIdentifier(checkDTO.getRealName());
                return result;
            }
        }

        return result;
    }

    /**
     * 保存解析数据到数据库
     */
    private Long saveParseDataToDatabase(Long userId, ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            // 首先保存解析记录
            Long parseRecordId = saveParseRecord(userId, parseResult, file);

            ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();
            if (result == null) {
                return parseRecordId;
            }

            // 保存联系方式
            if (requestDTO.getParseContactInfo() && result.getContactInfo() != null) {
                saveContactInfo(userId, result.getContactInfo());
            }

            // 保存教育经历
            log.debug("检查教育经历保存条件: parseEducation={}, educationExperience={}",
                requestDTO.getParseEducation(),
                result.getEducationExperience() != null ? result.getEducationExperience().size() : "null");

            if (requestDTO.getParseEducation() && result.getEducationExperience() != null) {
                log.info("开始保存教育经历: userId={}, 教育经历数量={}", userId, result.getEducationExperience().size());
                saveEducationHistory(userId, result.getEducationExperience());
            } else {
                log.warn("跳过教育经历保存: userId={}, parseEducation={}, educationExperience={}",
                    userId, requestDTO.getParseEducation(),
                    result.getEducationExperience() != null ? "有数据" : "无数据");
            }

            // 保存工作经历
            if (requestDTO.getParseWorkExperience() && result.getWorkExperience() != null) {
                saveWorkHistory(userId, result.getWorkExperience());
            }

            // 保存项目经历
            if (requestDTO.getParseProjectExperience() && result.getProjectExperience() != null) {
                saveProjectHistory(userId, result.getProjectExperience());
            }

            // 保存技能信息
            if (requestDTO.getParseSkills() && result.getOthers() != null) {
                saveSkills(userId, result.getOthers());
            }

            // 保存获奖记录
            if (requestDTO.getParseAwards() && result.getOthers() != null) {
                saveAwards(userId, result.getOthers());
            }

            // 保存培训经历
            if (requestDTO.getParseTraining() && result.getTrainingExperience() != null) {
                saveTrainingHistory(userId, result.getTrainingExperience());
            }

            log.info("保存解析数据到数据库成功: userId={}, parseRecordId={}", userId, parseRecordId);
            return parseRecordId;

        } catch (Exception e) {
            log.error("保存解析数据到数据库失败: userId={}", userId, e);
            throw new RuntimeException("保存解析数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存联系方式
     */
    private void saveContactInfo(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        List<UserContactMethods> contacts = conversionService.convertContactInfo(userId, contactInfo);
        for (UserContactMethods contact : contacts) {
            // 检查是否已存在相同联系方式
            UserContactMethods existing = contactMethodsMapper.selectByUserIdAndTypeAndValue(
                userId, contact.getContactType(), contact.getContactValue());
            if (existing == null) {
                contactMethodsMapper.insert(contact);
                log.debug("保存联系方式: userId={}, type={}, value={}",
                    userId, contact.getContactType(), contact.getContactValue());
            }
        }
    }

    /**
     * 保存教育经历
     */
    private void saveEducationHistory(Long userId, List<ThirdPartyParseResultDTO.EducationExperience> educationList) {
        log.info("开始转换教育经历数据: userId={}, 原始教育经历数量={}", userId, educationList.size());

        // 打印原始教育经历数据
        for (int i = 0; i < educationList.size(); i++) {
            ThirdPartyParseResultDTO.EducationExperience edu = educationList.get(i);
            log.info("原始教育经历[{}]: school={}, degree={}, major={}, startYear={}, endYear={}",
                i, edu.getSchoolName(), edu.getDegree(), edu.getMajor(),
                edu.getStartTimeYear(), edu.getEndTimeYear());
        }

        List<UserEducationHistory> educations = conversionService.convertEducationExperience(userId, educationList);
        log.info("转换后教育经历数量: userId={}, 转换后数量={}", userId, educations.size());

        for (UserEducationHistory education : educations) {
            try {
                educationMapper.insert(education);
                log.info("成功保存教育经历: userId={}, school={}, degree={}, degreeLevel={}, major={}",
                    userId, education.getSchoolName(), education.getDegree(),
                    education.getDegreeLevel(), education.getMajor());
            } catch (Exception e) {
                log.error("保存教育经历失败: userId={}, school={}, degree={}",
                    userId, education.getSchoolName(), education.getDegree(), e);
                throw e;
            }
        }
    }

    /**
     * 保存工作经历
     */
    private void saveWorkHistory(Long userId, List<ThirdPartyParseResultDTO.WorkExperience> workList) {
        List<UserWorkHistory> workHistories = conversionService.convertWorkExperience(userId, workList);
        for (UserWorkHistory workHistory : workHistories) {
            workHistoryMapper.insert(workHistory);
            log.debug("保存工作经历: userId={}, company={}, position={}",
                userId, workHistory.getCompanyName(), workHistory.getPositionName());
        }
    }

    /**
     * 保存项目经历
     */
    private void saveProjectHistory(Long userId, List<ThirdPartyParseResultDTO.ProjectExperience> projectList) {
        List<UserProjectHistory> projects = conversionService.convertProjectExperience(userId, projectList);
        for (UserProjectHistory project : projects) {
            projectHistoryMapper.insert(project);
            log.debug("保存项目经历: userId={}, project={}, role={}",
                userId, project.getProjectName(), project.getRole());
        }
    }

    /**
     * 保存技能信息
     */
    private void saveSkills(Long userId, ThirdPartyParseResultDTO.Others others) {
        List<UserSkills> skills = conversionService.convertSkills(userId, others);
        for (UserSkills skill : skills) {
            // 检查是否已存在相同技能
            UserSkills existing = skillsMapper.selectByUserIdAndSkillName(userId, skill.getSkillName());
            if (existing == null) {
                skillsMapper.insert(skill);
                log.debug("保存技能: userId={}, skill={}, type={}",
                    userId, skill.getSkillName(), skill.getSkillType());
            }
        }
    }

    /**
     * 保存获奖记录
     */
    private void saveAwards(Long userId, ThirdPartyParseResultDTO.Others others) {
        log.info("开始保存获奖记录: userId={}", userId);

        if (others.getAwards() == null || others.getAwards().isEmpty()) {
            log.info("没有获奖记录数据: userId={}", userId);
            return;
        }

        log.info("原始获奖记录数量: userId={}, 获奖记录数量={}", userId, others.getAwards().size());

        // 打印原始获奖记录数据
        for (int i = 0; i < others.getAwards().size(); i++) {
            String award = others.getAwards().get(i);
            log.info("原始获奖记录[{}]: {}", i, award);
        }

        List<UserAwards> awards = conversionService.convertAwards(userId, others);
        log.info("转换后获奖记录数量: userId={}, 转换后数量={}", userId, awards.size());

        for (UserAwards award : awards) {
            try {
                // 检查是否已存在相同获奖记录
                UserAwards existing = awardsMapper.selectByUserIdAndAwardName(userId, award.getAwardName());
                if (existing == null) {
                    awardsMapper.insert(award);
                    log.info("成功保存获奖记录: userId={}, awardName={}, level={}",
                        userId, award.getAwardName(), award.getAwardLevel());
                } else {
                    log.info("获奖记录已存在，跳过: userId={}, awardName={}",
                        userId, award.getAwardName());
                }
            } catch (Exception e) {
                log.error("保存获奖记录失败: userId={}, awardName={}",
                    userId, award.getAwardName(), e);
                throw e;
            }
        }
    }

    /**
     * 保存培训经历
     */
    private void saveTrainingHistory(Long userId, List<ThirdPartyParseResultDTO.TrainingExperience> trainingList) {
        log.info("开始保存培训经历: userId={}", userId);

        if (trainingList == null || trainingList.isEmpty()) {
            log.info("没有培训经历数据: userId={}", userId);
            return;
        }

        log.info("原始培训经历数量: userId={}, 培训经历数量={}", userId, trainingList.size());

        // 打印原始培训经历数据
        for (int i = 0; i < trainingList.size(); i++) {
            ThirdPartyParseResultDTO.TrainingExperience training = trainingList.get(i);
            log.info("原始培训经历[{}]: organizationName={}, subject={}, startYear={}, endYear={}",
                i, training.getOrganizationName(), training.getSubject(),
                training.getStartTimeYear(), training.getEndTimeYear());
        }

        List<UserTraining> trainings = conversionService.convertTrainingExperience(userId, trainingList);
        log.info("转换后培训经历数量: userId={}, 转换后数量={}", userId, trainings.size());

        for (UserTraining training : trainings) {
            try {
                // 检查是否已存在相同培训经历
                UserTraining existing = trainingMapper.selectByUserIdAndNameAndProvider(
                    userId, training.getTrainingName(), training.getTrainingProvider());
                if (existing == null) {
                    trainingMapper.insert(training);
                    log.info("成功保存培训经历: userId={}, trainingName={}, provider={}",
                        userId, training.getTrainingName(), training.getTrainingProvider());
                } else {
                    log.info("培训经历已存在，跳过: userId={}, trainingName={}, provider={}",
                        userId, training.getTrainingName(), training.getTrainingProvider());
                }
            } catch (Exception e) {
                log.error("保存培训经历失败: userId={}, trainingName={}, provider={}",
                    userId, training.getTrainingName(), training.getTrainingProvider(), e);
                throw e;
            }
        }
    }

    /**
     * 保存解析记录
     */
    private Long saveParseRecord(Long userId, ThirdPartyParseResultDTO parseResult, MultipartFile file) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setUserId(userId);
            record.setOriginalFilename(file.getOriginalFilename());
            record.setFileSize(file.getSize());
            record.setFileType(getFileExtension(file.getOriginalFilename()));
            record.setThirdPartyId(parseResult.getCvId());
            record.setErrorMessage(parseResult.getErrorMessage());
            record.setParseStatus(parseResult.getErrorCode() == 0 ?
                BatchResumeParseConstants.ParseStatus.SUCCESS :
                BatchResumeParseConstants.ParseStatus.FAILURE);

            // 设置解析结果的JSON字符串 - 保留第三方接口原始数据结构
            try {
                // 将整个第三方解析结果转换为JSON字符串存储，保持原始数据结构
                record.setParseResult(convertThirdPartyResultToJson(parseResult));
            } catch (Exception e) {
                log.warn("转换第三方解析结果为JSON失败: userId={}", userId, e);
                record.setParseResult(BatchResumeParseConstants.Json.EMPTY_OBJECT);
            }

            parseRecordsMapper.insert(record);
            log.debug("保存解析记录成功: userId={}, recordId={}, thirdPartyId={}",
                userId, record.getRecordId(), record.getThirdPartyId());

            return record.getRecordId();

        } catch (Exception e) {
            log.error("保存解析记录失败: userId={}", userId, e);
            throw new RuntimeException("保存解析记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将第三方解析结果转换为JSON字符串（保留原始数据结构）
     */
    private String convertThirdPartyResultToJson(ThirdPartyParseResultDTO parseResult) {
        try {
            // 使用Jackson ObjectMapper将整个第三方解析结果转换为JSON
            // 这样可以保留完整的原始数据结构
            return objectMapper.writeValueAsString(parseResult);
        } catch (Exception e) {
            log.warn("使用ObjectMapper转换第三方解析结果为JSON时发生异常", e);
            // 如果ObjectMapper失败，使用备用方法
            return convertToJsonFallback(parseResult);
        }
    }

    /**
     * 备用的JSON转换方法（简化版本）
     */
    private String convertToJsonFallback(ThirdPartyParseResultDTO parseResult) {
        try {
            StringBuilder json = new StringBuilder();
            json.append("{");

            // 基本信息
            json.append("\"errorCode\":").append(parseResult.getErrorCode()).append(",");
            json.append("\"errorMessage\":\"").append(escapeJson(parseResult.getErrorMessage())).append("\",");
            json.append("\"cvId\":\"").append(escapeJson(parseResult.getCvId())).append("\"");

            // 如果有解析结果，添加解析结果
            if (parseResult.getParsingResult() != null) {
                json.append(",\"parsingResult\":");
                json.append(convertParsingResultToJson(parseResult.getParsingResult()));
            }

            json.append("}");
            return json.toString();

        } catch (Exception e) {
            log.warn("备用JSON转换方法也失败", e);
            return BatchResumeParseConstants.Json.ERROR_OBJECT;
        }
    }

    /**
     * 转换解析结果部分为JSON
     */
    private String convertParsingResultToJson(ThirdPartyParseResultDTO.ParsingResult parsingResult) {
        StringBuilder json = new StringBuilder();
        json.append("{");

        if (parsingResult.getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parsingResult.getBasicInfo();
            json.append("\"basicInfo\":{");
            json.append("\"name\":\"").append(escapeJson(basicInfo.getName())).append("\",");
            json.append("\"gender\":\"").append(escapeJson(basicInfo.getGender())).append("\",");
            json.append("\"age\":").append(basicInfo.getAge() != null ? basicInfo.getAge() : "null").append(",");
            json.append("\"degree\":\"").append(escapeJson(basicInfo.getDegree())).append("\"");
            json.append("},");
        }

        if (parsingResult.getContactInfo() != null) {
            ThirdPartyParseResultDTO.ContactInfo contactInfo = parsingResult.getContactInfo();
            json.append("\"contactInfo\":{");
            json.append("\"phoneNumber\":\"").append(escapeJson(contactInfo.getPhoneNumber())).append("\",");
            json.append("\"email\":\"").append(escapeJson(contactInfo.getEmail())).append("\"");
            json.append("},");
        }

        // 移除最后的逗号
        if (json.charAt(json.length() - 1) == ',') {
            json.setLength(json.length() - 1);
        }

        json.append("}");
        return json.toString();
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
    }



    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return BatchResumeParseConstants.FileExtension.UNKNOWN;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return BatchResumeParseConstants.FileExtension.UNKNOWN;
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 创建用户结果内部类
     */
    private static class CreateUserResult {
        private final Long userId;
        private final Long parseRecordId;

        public CreateUserResult(Long userId, Long parseRecordId) {
            this.userId = userId;
            this.parseRecordId = parseRecordId;
        }

        public Long getUserId() {
            return userId;
        }

        public Long getParseRecordId() {
            return parseRecordId;
        }
    }

    /**
     * 解析生日字符串为LocalDate
     */
    private LocalDate parseBirthday(String dateOfBirth) {
        if (dateOfBirth == null || dateOfBirth.trim().isEmpty()) {
            return null;
        }

        String cleanDate = dateOfBirth.trim();
        log.debug("开始解析生日: {}", cleanDate);

        // 使用常量中定义的日期格式
        String[] patterns = BatchResumeParseConstants.DatePatterns.BIRTHDAY_PATTERNS;

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                LocalDate birthday = LocalDate.parse(cleanDate, formatter);
                log.debug("成功解析生日: {} -> {}", cleanDate, birthday);
                return birthday;
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
                log.trace("格式 {} 解析失败: {}", pattern, e.getMessage());
            }
        }

        // 如果所有格式都失败，尝试提取数字并手动构造
        try {
            return parseNumericBirthday(cleanDate);
        } catch (Exception e) {
            log.warn("无法解析生日格式: {}", cleanDate);
            throw new IllegalArgumentException("无法解析生日格式: " + cleanDate, e);
        }
    }

    /**
     * 从包含数字的字符串中提取生日
     */
    private LocalDate parseNumericBirthday(String dateStr) {
        // 提取所有数字
        String numbers = dateStr.replaceAll("[^0-9]", "");

        if (numbers.length() == 8) {
            // YYYYMMDD格式
            int year = Integer.parseInt(numbers.substring(0, 4));
            int month = Integer.parseInt(numbers.substring(4, 6));
            int day = Integer.parseInt(numbers.substring(6, 8));
            return LocalDate.of(year, month, day);
        } else if (numbers.length() >= 6) {
            // 尝试提取年月日
            String[] parts = dateStr.split("[^0-9]+");
            if (parts.length >= 3) {
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);

                // 如果年份是两位数，假设是19xx或20xx
                if (year < 100) {
                    year += (year > 50) ? 1900 : 2000;
                }

                return LocalDate.of(year, month, day);
            }
        }

        throw new IllegalArgumentException("无法从字符串中提取有效的日期信息: " + dateStr);
    }

    /**
     * 创建用户认证信息
     */
    private void createUserAuth(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        try {
            // 优先使用邮箱创建认证信息
            if (contactInfo.getEmail() != null && !contactInfo.getEmail().trim().isEmpty()) {
                String email = contactInfo.getEmail().trim();

                // 检查邮箱是否已存在
                if (!isEmailExists(email)) {
                    UserAuth emailAuth = new UserAuth();
                    emailAuth.setUserId(userId);
                    emailAuth.setIdentityType(BatchResumeParseConstants.IdentityType.EMAIL);
                    emailAuth.setIdentifier(email);
                    emailAuth.setVerified(BatchResumeParseConstants.VerificationStatus.UNVERIFIED);
                    emailAuth.setLastLoginAt(LocalDateTime.now());

                    userAuthMapper.insert(emailAuth);
                    log.info("创建邮箱认证信息成功: userId={}, email={}", userId, email);
                } else {
                    log.warn("邮箱已存在，跳过创建认证信息: email={}", email);
                }
            }

            // 如果有手机号，也创建手机号认证信息
            if (contactInfo.getPhoneNumber() != null && !contactInfo.getPhoneNumber().trim().isEmpty()) {
                String phone = contactInfo.getPhoneNumber().trim();

                // 检查手机号是否已存在
                if (!isPhoneExists(phone)) {
                    UserAuth phoneAuth = new UserAuth();
                    phoneAuth.setUserId(userId);
                    phoneAuth.setIdentityType(BatchResumeParseConstants.IdentityType.PHONE);
                    phoneAuth.setIdentifier(phone);
                    phoneAuth.setVerified(BatchResumeParseConstants.VerificationStatus.UNVERIFIED);
                    phoneAuth.setLastLoginAt(LocalDateTime.now());

                    userAuthMapper.insert(phoneAuth);
                    log.info("创建手机号认证信息成功: userId={}, phone={}", userId, phone);
                } else {
                    log.warn("手机号已存在，跳过创建认证信息: phone={}", phone);
                }
            }

        } catch (Exception e) {
            log.error("创建用户认证信息失败: userId={}", userId, e);
            // 不抛出异常，避免影响整个用户创建流程
        }
    }

    /**
     * 检查邮箱是否已存在
     */
    private boolean isEmailExists(String email) {
        return userAuthMapper.selectCount(
            new LambdaQueryWrapper<UserAuth>()
                .eq(UserAuth::getIdentityType, BatchResumeParseConstants.IdentityType.EMAIL)
                .eq(UserAuth::getIdentifier, email)
        ) > 0;
    }

    /**
     * 检查手机号是否已存在
     */
    private boolean isPhoneExists(String phone) {
        return userAuthMapper.selectCount(
            new LambdaQueryWrapper<UserAuth>()
                .eq(UserAuth::getIdentityType, BatchResumeParseConstants.IdentityType.PHONE)
                .eq(UserAuth::getIdentifier, phone)
        ) > 0;
    }
}
