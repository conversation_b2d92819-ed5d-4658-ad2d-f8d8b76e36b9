package com.tinyzk.user.center.service.impl;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.dto.UserDuplicationCheckDTO;
import com.tinyzk.user.center.entity.UserAuth;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import com.tinyzk.user.center.entity.UserContactMethods;
import com.tinyzk.user.center.entity.UserEducationHistory;
import com.tinyzk.user.center.entity.UserProjectHistory;
import com.tinyzk.user.center.entity.UserSkills;
import com.tinyzk.user.center.entity.UserWorkHistory;
import com.tinyzk.user.center.mapper.AdminUserMapper;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserContactMethodsMapper;
import com.tinyzk.user.center.mapper.UserEducationHistoryMapper;
import com.tinyzk.user.center.mapper.UserProjectHistoryMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.mapper.UserSkillsMapper;
import com.tinyzk.user.center.mapper.UserWorkHistoryMapper;
import com.tinyzk.user.center.service.BatchResumeParseService;
import com.tinyzk.user.center.service.ResumeDataConversionService;
import com.tinyzk.user.center.service.ThirdPartyResumeParseService;
import com.tinyzk.user.center.util.BatchProcessProgressTracker;
import com.tinyzk.user.center.util.FileValidationUtil;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 批量简历解析服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BatchResumeParseServiceImpl implements BatchResumeParseService {

    private final ThirdPartyResumeParseService thirdPartyResumeParseService;
    private final ResumeDataConversionService conversionService;
    private final AdminUserMapper adminUserMapper;
    private final UserBaseMapper userBaseMapper;
    private final UserAuthMapper userAuthMapper;
    private final UserProfileMapper userProfileMapper;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final UserContactMethodsMapper contactMethodsMapper;
    private final UserEducationHistoryMapper educationMapper;
    private final UserWorkHistoryMapper workHistoryMapper;
    private final UserProjectHistoryMapper projectHistoryMapper;
    private final UserSkillsMapper skillsMapper;
    private final FileValidationUtil fileValidationUtil;
    private final ObjectMapper objectMapper;

    @Override
    public BatchResumeUploadResultVO batchParseResumes(BatchResumeUploadRequestDTO requestDTO) {
        LocalDateTime startTime = LocalDateTime.now();
        String batchId = "batch_" + System.currentTimeMillis();
        log.info("开始批量解析简历，batchId={}, 文件数量: {}", batchId, requestDTO.getFiles().length);

        // 验证文件
        try {
            fileValidationUtil.validateBatchFiles(requestDTO.getFiles());
            fileValidationUtil.validateConcurrencyParams(requestDTO.getMaxConcurrency(), requestDTO.getTimeoutSeconds());
            log.info("文件验证通过，batchId={}", batchId);
        } catch (Exception e) {
            log.error("文件验证失败，batchId={}", batchId, e);
            throw e;
        }

        // 创建进度跟踪器
        BatchProcessProgressTracker progressTracker = new BatchProcessProgressTracker(batchId, requestDTO.getFiles().length);

        // 初始化结果对象
        BatchResumeUploadResultVO result = new BatchResumeUploadResultVO();
        result.setStartTime(startTime);

        // 初始化统计信息
        BatchResumeUploadResultVO.ProcessStatistics statistics = new BatchResumeUploadResultVO.ProcessStatistics();
        statistics.setTotalFiles(requestDTO.getFiles().length);

        // 初始化文件处理结果列表
        List<BatchResumeUploadResultVO.FileProcessResult> fileResults = new ArrayList<>();

        // 创建线程池
        int maxConcurrency = Math.min(requestDTO.getMaxConcurrency(), 10); // 最大不超过10个并发
        ExecutorService executorService = Executors.newFixedThreadPool(maxConcurrency);

        // 原子计数器
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);
        AtomicInteger newUserCount = new AtomicInteger(0);
        AtomicInteger updatedUserCount = new AtomicInteger(0);
        
        try {
            // 创建异步任务列表
            List<CompletableFuture<BatchResumeUploadResultVO.FileProcessResult>> futures = new ArrayList<>();
            
            for (MultipartFile file : requestDTO.getFiles()) {
                CompletableFuture<BatchResumeUploadResultVO.FileProcessResult> future =
                    CompletableFuture.supplyAsync(() -> processFile(file, requestDTO, progressTracker), executorService)
                        .orTimeout(requestDTO.getTimeoutSeconds(), TimeUnit.SECONDS)
                        .exceptionally(throwable -> {
                            log.error("处理文件 {} 时发生异常", file.getOriginalFilename(), throwable);
                            BatchResumeUploadResultVO.FileProcessResult errorResult = createErrorResult(file, throwable);
                            // 更新进度跟踪器
                            progressTracker.recordFileProcessed(file.getOriginalFilename(), false, false,
                                errorResult.getProcessingTimeMs() != null ? errorResult.getProcessingTimeMs() : 0);
                            return errorResult;
                        });
                futures.add(future);
            }
            
            // 等待所有任务完成
            for (CompletableFuture<BatchResumeUploadResultVO.FileProcessResult> future : futures) {
                try {
                    BatchResumeUploadResultVO.FileProcessResult fileResult = future.get();
                    fileResults.add(fileResult);
                    
                    // 更新统计信息
                    switch (fileResult.getStatus()) {
                        case SUCCESS:
                            successCount.incrementAndGet();
                            if (fileResult.getUserOperation() == BatchResumeUploadResultVO.UserOperationType.CREATED) {
                                newUserCount.incrementAndGet();
                            } else if (fileResult.getUserOperation() == BatchResumeUploadResultVO.UserOperationType.UPDATED) {
                                updatedUserCount.incrementAndGet();
                            }
                            break;
                        case FAILURE:
                            failureCount.incrementAndGet();
                            break;
                        case SKIPPED:
                            skippedCount.incrementAndGet();
                            break;
                    }
                } catch (Exception e) {
                    log.error("获取文件处理结果时发生异常", e);
                    failureCount.incrementAndGet();
                }
            }
            
        } finally {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 完善统计信息
        statistics.setSuccessCount(successCount.get());
        statistics.setFailureCount(failureCount.get());
        statistics.setSkippedCount(skippedCount.get());
        statistics.setNewUserCount(newUserCount.get());
        statistics.setUpdatedUserCount(updatedUserCount.get());
        statistics.setSuccessRate((double) successCount.get() / statistics.getTotalFiles());
        
        // 设置结果
        LocalDateTime endTime = LocalDateTime.now();
        result.setEndTime(endTime);
        result.setTotalDurationMs(java.time.Duration.between(startTime, endTime).toMillis());
        result.setStatistics(statistics);
        result.setFileResults(fileResults);
        
        // 记录批量处理完成
        progressTracker.recordBatchCompleted();

        log.info("批量解析简历完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
                statistics.getTotalFiles(), statistics.getSuccessCount(),
                statistics.getFailureCount(), statistics.getSkippedCount(),
                result.getTotalDurationMs());

        return result;
    }

    /**
     * 处理单个文件
     */
    private BatchResumeUploadResultVO.FileProcessResult processFile(MultipartFile file, BatchResumeUploadRequestDTO requestDTO, BatchProcessProgressTracker progressTracker) {
        long startTime = System.currentTimeMillis();
        BatchResumeUploadResultVO.FileProcessResult fileResult = new BatchResumeUploadResultVO.FileProcessResult();
        fileResult.setFileName(file.getOriginalFilename());
        fileResult.setFileSize(file.getSize());

        try {
            log.info("开始处理文件: {}", file.getOriginalFilename());

            // 调用第三方服务解析简历
            ThirdPartyParseResultDTO parseResult = thirdPartyResumeParseService.parseResume(file);

            // 从解析结果中提取用户信息
            BatchResumeUploadResultVO.UserInfoSummary userInfo = extractUserInfo(parseResult);
            fileResult.setUserInfo(userInfo);

            // 检查用户是否重复
            UserDuplicationCheckDTO.CheckResult duplicationResult = checkUserDuplication(userInfo);

            Long finalUserId;
            Long parseRecordId = null;
            BatchResumeUploadResultVO.UserOperationType userOperation;

            if (duplicationResult.getIsDuplicate()) {
                // 用户已存在
                finalUserId = duplicationResult.getDuplicateUserId();

                if (requestDTO.getOverwriteExisting()) {
                    // 更新现有用户信息
                    updateExistingUser(finalUserId, parseResult, requestDTO, file);
                    userOperation = BatchResumeUploadResultVO.UserOperationType.UPDATED;
                    fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
                    // 为更新的用户也保存解析记录
                    parseRecordId = saveParseRecord(finalUserId, parseResult, file);
                } else {
                    // 跳过重复用户
                    userOperation = BatchResumeUploadResultVO.UserOperationType.SKIPPED;
                    fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SKIPPED);
                }
            } else {
                // 创建新用户
                finalUserId = createNewUser(parseResult, requestDTO, file);
                userOperation = BatchResumeUploadResultVO.UserOperationType.CREATED;
                fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
                // 解析记录ID已经在createNewUser中创建了，这里需要获取
                // 为了简化，我们在这里再次保存解析记录
                parseRecordId = saveParseRecord(finalUserId, parseResult, file);
            }

            fileResult.setUserId(finalUserId);
            fileResult.setUserOperation(userOperation);
            fileResult.setParseRecordId(parseRecordId);

        } catch (Exception e) {
            log.error("处理文件 {} 失败", file.getOriginalFilename(), e);
            fileResult.setStatus(BatchResumeUploadResultVO.ProcessStatus.FAILURE);
            fileResult.setErrorMessage(e.getMessage());
            fileResult.setErrorType(determineErrorType(e));
        }

        long processingTime = System.currentTimeMillis() - startTime;
        fileResult.setProcessingTimeMs(processingTime);

        // 更新进度跟踪器
        boolean success = fileResult.getStatus() == BatchResumeUploadResultVO.ProcessStatus.SUCCESS;
        boolean skipped = fileResult.getStatus() == BatchResumeUploadResultVO.ProcessStatus.SKIPPED;
        progressTracker.recordFileProcessed(file.getOriginalFilename(), success, skipped, processingTime);

        return fileResult;
    }

    /**
     * 从第三方解析结果中提取用户信息
     */
    private BatchResumeUploadResultVO.UserInfoSummary extractUserInfo(ThirdPartyParseResultDTO parseResult) {
        BatchResumeUploadResultVO.UserInfoSummary userInfo = new BatchResumeUploadResultVO.UserInfoSummary();

        if (parseResult.getParsingResult() != null) {
            ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();

            // 提取基本信息
            if (result.getBasicInfo() != null) {
                ThirdPartyParseResultDTO.BasicInfo basicInfo = result.getBasicInfo();
                userInfo.setName(basicInfo.getName());
                userInfo.setGender(convertGender(basicInfo.getGender()));
                userInfo.setAge(basicInfo.getAge());

                // 计算工作年限（简单估算）
                if (basicInfo.getAge() != null && basicInfo.getAge() > 22) {
                    userInfo.setWorkExperience((basicInfo.getAge() - 22) + "年");
                }

                userInfo.setEducation(basicInfo.getDegree());
            }

            // 提取联系方式
            if (result.getContactInfo() != null) {
                ThirdPartyParseResultDTO.ContactInfo contactInfo = result.getContactInfo();
                userInfo.setPhone(contactInfo.getPhoneNumber());
                userInfo.setEmail(contactInfo.getEmail());
            }
        }

        return userInfo;
    }

    /**
     * 转换性别信息
     */
    private String convertGender(String gender) {
        if (gender == null) return "未知";
        switch (gender) {
            case "男": return "男";
            case "女": return "女";
            default: return "未知";
        }
    }

    /**
     * 检查用户重复（重载方法）
     */
    private UserDuplicationCheckDTO.CheckResult checkUserDuplication(BatchResumeUploadResultVO.UserInfoSummary userInfo) {
        UserDuplicationCheckDTO checkDTO = new UserDuplicationCheckDTO();
        checkDTO.setPhone(userInfo.getPhone());
        checkDTO.setEmail(userInfo.getEmail());
        checkDTO.setRealName(userInfo.getName());

        return checkUserDuplication(checkDTO);
    }

    /**
     * 创建新用户
     */
    private Long createNewUser(ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            // 创建用户基础信息
            UserBase userBase = new UserBase();
            userBase.setRealNameVerified(false);
            userBase.setStatus(1); // 正常状态

            // 如果有姓名信息，设置真实姓名
            if (parseResult.getParsingResult() != null &&
                parseResult.getParsingResult().getBasicInfo() != null &&
                parseResult.getParsingResult().getBasicInfo().getName() != null) {
                userBase.setRealName(parseResult.getParsingResult().getBasicInfo().getName());
            }

            userBaseMapper.insert(userBase);
            Long userId = userBase.getUserId();

            log.info("创建用户基础信息成功: userId={}", userId);

            // 创建用户认证信息（如果有手机号）
            if (parseResult.getParsingResult() != null &&
                parseResult.getParsingResult().getContactInfo() != null &&
                parseResult.getParsingResult().getContactInfo().getPhoneNumber() != null) {

                UserAuth userAuth = new UserAuth();
                userAuth.setUserId(userId);
                userAuth.setIdentityType("PHONE");
                userAuth.setIdentifier(parseResult.getParsingResult().getContactInfo().getPhoneNumber());
                userAuth.setVerified(1);
                userAuthMapper.insert(userAuth);

                log.info("创建用户认证信息成功: userId={}, phone={}", userId, userAuth.getIdentifier());
            }

            // 创建用户资料
            createUserProfile(userId, parseResult);

            // 保存解析记录和详细信息
            Long parseRecordId = saveParseDataToDatabase(userId, parseResult, requestDTO, file);

            // 在文件处理结果中记录解析记录ID
            // 这个会在processFile方法中设置

            return userId;

        } catch (Exception e) {
            log.error("创建新用户失败", e);
            throw new RuntimeException("创建新用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新现有用户
     */
    private void updateExistingUser(Long userId, ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            log.info("更新现有用户: userId={}", userId);

            // 更新用户资料
            updateUserProfile(userId, parseResult);

            // 更新解析数据（如果允许覆盖）
            if (requestDTO.getOverwriteExisting()) {
                saveParseDataToDatabase(userId, parseResult, requestDTO, file);
            }

            log.info("更新用户信息成功: userId={}", userId);

        } catch (Exception e) {
            log.error("更新用户失败: userId={}", userId, e);
            throw new RuntimeException("更新用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建错误结果
     */
    private BatchResumeUploadResultVO.FileProcessResult createErrorResult(MultipartFile file, Throwable throwable) {
        BatchResumeUploadResultVO.FileProcessResult result = new BatchResumeUploadResultVO.FileProcessResult();
        result.setFileName(file.getOriginalFilename());
        result.setFileSize(file.getSize());
        result.setStatus(BatchResumeUploadResultVO.ProcessStatus.FAILURE);
        result.setErrorMessage(throwable.getMessage());
        result.setErrorType(determineErrorType(throwable));
        return result;
    }

    /**
     * 确定错误类型
     */
    private BatchResumeUploadResultVO.ErrorType determineErrorType(Throwable throwable) {
        if (throwable instanceof java.util.concurrent.TimeoutException) {
            return BatchResumeUploadResultVO.ErrorType.TIMEOUT;
        }

        String message = throwable.getMessage();
        if (message != null) {
            message = message.toLowerCase();

            if (message.contains("文件") || message.contains("file") ||
                message.contains("格式") || message.contains("大小") ||
                message.contains("类型")) {
                return BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION;
            }

            if (message.contains("api") || message.contains("解析") ||
                message.contains("parse") || message.contains("第三方") ||
                message.contains("网络") || message.contains("连接")) {
                return BatchResumeUploadResultVO.ErrorType.PARSE_API_ERROR;
            }

            if (message.contains("数据库") || message.contains("database") ||
                message.contains("sql") || message.contains("insert") ||
                message.contains("update") || message.contains("constraint")) {
                return BatchResumeUploadResultVO.ErrorType.DATABASE_ERROR;
            }

            if (message.contains("转换") || message.contains("conversion") ||
                message.contains("mapping") || message.contains("数据")) {
                return BatchResumeUploadResultVO.ErrorType.DATA_CONVERSION;
            }
        }

        // 根据异常类型判断
        if (throwable instanceof java.sql.SQLException ||
            throwable instanceof org.springframework.dao.DataAccessException) {
            return BatchResumeUploadResultVO.ErrorType.DATABASE_ERROR;
        }

        if (throwable instanceof java.io.IOException ||
            throwable instanceof java.net.SocketTimeoutException) {
            return BatchResumeUploadResultVO.ErrorType.PARSE_API_ERROR;
        }

        if (throwable instanceof IllegalArgumentException ||
            throwable instanceof NumberFormatException) {
            return BatchResumeUploadResultVO.ErrorType.DATA_CONVERSION;
        }

        return BatchResumeUploadResultVO.ErrorType.UNKNOWN;
    }

    /**
     * 创建用户资料
     */
    private void createUserProfile(Long userId, ThirdPartyParseResultDTO parseResult) {
        if (parseResult.getParsingResult() == null) return;

        UserProfile userProfile = new UserProfile();
        userProfile.setUserId(userId);

        // 设置基本信息
        if (parseResult.getParsingResult().getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parseResult.getParsingResult().getBasicInfo();

            // 使用姓名作为昵称
            if (basicInfo.getName() != null) {
                userProfile.setNickname(basicInfo.getName());
            }

            // 设置性别
            if (basicInfo.getGender() != null) {
                switch (basicInfo.getGender()) {
                    case "男":
                        userProfile.setGender(1);
                        break;
                    case "女":
                        userProfile.setGender(2);
                        break;
                    default:
                        userProfile.setGender(0);
                        break;
                }
            }

            // 设置地区信息
            if (basicInfo.getCurrentLocation() != null) {
                userProfile.setRegionName(basicInfo.getCurrentLocation());
            }
        }

        userProfileMapper.insert(userProfile);
        log.info("创建用户资料成功: userId={}", userId);
    }

    /**
     * 更新用户资料
     */
    private void updateUserProfile(Long userId, ThirdPartyParseResultDTO parseResult) {
        if (parseResult.getParsingResult() == null) return;

        // 查询现有用户资料
        UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
        if (existingProfile == null) {
            // 如果不存在，则创建
            createUserProfile(userId, parseResult);
            return;
        }

        // 更新基本信息
        if (parseResult.getParsingResult().getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parseResult.getParsingResult().getBasicInfo();

            // 更新昵称（如果原来没有）
            if (existingProfile.getNickname() == null && basicInfo.getName() != null) {
                existingProfile.setNickname(basicInfo.getName());
            }

            // 更新性别（如果原来没有）
            if (existingProfile.getGender() == null || existingProfile.getGender() == 0) {
                if (basicInfo.getGender() != null) {
                    switch (basicInfo.getGender()) {
                        case "男":
                            existingProfile.setGender(1);
                            break;
                        case "女":
                            existingProfile.setGender(2);
                            break;
                    }
                }
            }

            // 更新地区信息（如果原来没有）
            if (existingProfile.getRegionName() == null && basicInfo.getCurrentLocation() != null) {
                existingProfile.setRegionName(basicInfo.getCurrentLocation());
            }
        }

        userProfileMapper.updateById(existingProfile);
        log.info("更新用户资料成功: userId={}", userId);
    }

    @Override
    public UserDuplicationCheckDTO.CheckResult checkUserDuplication(UserDuplicationCheckDTO checkDTO) {
        UserDuplicationCheckDTO.CheckResult result = new UserDuplicationCheckDTO.CheckResult();
        result.setIsDuplicate(false);

        // 根据用户反馈，优先根据手机号和姓名进行去重
        if (checkDTO.getPhone() != null && checkDTO.getRealName() != null) {
            // 先尝试手机号+姓名匹配
            Long userId = adminUserMapper.selectUserIdByPhoneAndRealName(checkDTO.getPhone(), checkDTO.getRealName());
            if (userId != null) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userId);
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.PHONE);
                result.setMatchedIdentifier(checkDTO.getPhone());
                return result;
            }
        }

        // 如果手机号+姓名没有匹配，再尝试单独手机号匹配
        if (checkDTO.getPhone() != null) {
            Long userId = adminUserMapper.selectUserIdByPhone(checkDTO.getPhone());
            if (userId != null) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userId);
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.PHONE);
                result.setMatchedIdentifier(checkDTO.getPhone());
                return result;
            }
        }

        // 最后尝试姓名匹配（可能有多个同名用户，取第一个）
        if (checkDTO.getRealName() != null) {
            List<Long> userIds = adminUserMapper.selectUserIdsByRealName(checkDTO.getRealName());
            if (userIds != null && !userIds.isEmpty()) {
                result.setIsDuplicate(true);
                result.setDuplicateUserId(userIds.get(0));
                result.setMatchType(UserDuplicationCheckDTO.DuplicateMatchType.NAME_AND_ID_CARD);
                result.setMatchedIdentifier(checkDTO.getRealName());
                return result;
            }
        }

        return result;
    }

    /**
     * 保存解析数据到数据库
     */
    private Long saveParseDataToDatabase(Long userId, ThirdPartyParseResultDTO parseResult, BatchResumeUploadRequestDTO requestDTO, MultipartFile file) {
        try {
            // 首先保存解析记录
            Long parseRecordId = saveParseRecord(userId, parseResult, file);

            ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();
            if (result == null) {
                return parseRecordId;
            }

            // 保存联系方式
            if (requestDTO.getParseContactInfo() && result.getContactInfo() != null) {
                saveContactInfo(userId, result.getContactInfo());
            }

            // 保存教育经历
            log.debug("检查教育经历保存条件: parseEducation={}, educationExperience={}",
                requestDTO.getParseEducation(),
                result.getEducationExperience() != null ? result.getEducationExperience().size() : "null");

            if (requestDTO.getParseEducation() && result.getEducationExperience() != null) {
                log.info("开始保存教育经历: userId={}, 教育经历数量={}", userId, result.getEducationExperience().size());
                saveEducationHistory(userId, result.getEducationExperience());
            } else {
                log.warn("跳过教育经历保存: userId={}, parseEducation={}, educationExperience={}",
                    userId, requestDTO.getParseEducation(),
                    result.getEducationExperience() != null ? "有数据" : "无数据");
            }

            // 保存工作经历
            if (requestDTO.getParseWorkExperience() && result.getWorkExperience() != null) {
                saveWorkHistory(userId, result.getWorkExperience());
            }

            // 保存项目经历
            if (requestDTO.getParseProjectExperience() && result.getProjectExperience() != null) {
                saveProjectHistory(userId, result.getProjectExperience());
            }

            // 保存技能信息
            if (requestDTO.getParseSkills() && result.getOthers() != null) {
                saveSkills(userId, result.getOthers());
            }

            log.info("保存解析数据到数据库成功: userId={}, parseRecordId={}", userId, parseRecordId);
            return parseRecordId;

        } catch (Exception e) {
            log.error("保存解析数据到数据库失败: userId={}", userId, e);
            throw new RuntimeException("保存解析数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存联系方式
     */
    private void saveContactInfo(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        List<UserContactMethods> contacts = conversionService.convertContactInfo(userId, contactInfo);
        for (UserContactMethods contact : contacts) {
            // 检查是否已存在相同联系方式
            UserContactMethods existing = contactMethodsMapper.selectByUserIdAndTypeAndValue(
                userId, contact.getContactType(), contact.getContactValue());
            if (existing == null) {
                contactMethodsMapper.insert(contact);
                log.debug("保存联系方式: userId={}, type={}, value={}",
                    userId, contact.getContactType(), contact.getContactValue());
            }
        }
    }

    /**
     * 保存教育经历
     */
    private void saveEducationHistory(Long userId, List<ThirdPartyParseResultDTO.EducationExperience> educationList) {
        log.info("开始转换教育经历数据: userId={}, 原始教育经历数量={}", userId, educationList.size());

        // 打印原始教育经历数据
        for (int i = 0; i < educationList.size(); i++) {
            ThirdPartyParseResultDTO.EducationExperience edu = educationList.get(i);
            log.info("原始教育经历[{}]: school={}, degree={}, major={}, startYear={}, endYear={}",
                i, edu.getSchoolName(), edu.getDegree(), edu.getMajor(),
                edu.getStartTimeYear(), edu.getEndTimeYear());
        }

        List<UserEducationHistory> educations = conversionService.convertEducationExperience(userId, educationList);
        log.info("转换后教育经历数量: userId={}, 转换后数量={}", userId, educations.size());

        for (UserEducationHistory education : educations) {
            try {
                educationMapper.insert(education);
                log.info("成功保存教育经历: userId={}, school={}, degree={}, degreeLevel={}, major={}",
                    userId, education.getSchoolName(), education.getDegree(),
                    education.getDegreeLevel(), education.getMajor());
            } catch (Exception e) {
                log.error("保存教育经历失败: userId={}, school={}, degree={}",
                    userId, education.getSchoolName(), education.getDegree(), e);
                throw e;
            }
        }
    }

    /**
     * 保存工作经历
     */
    private void saveWorkHistory(Long userId, List<ThirdPartyParseResultDTO.WorkExperience> workList) {
        List<UserWorkHistory> workHistories = conversionService.convertWorkExperience(userId, workList);
        for (UserWorkHistory workHistory : workHistories) {
            workHistoryMapper.insert(workHistory);
            log.debug("保存工作经历: userId={}, company={}, position={}",
                userId, workHistory.getCompanyName(), workHistory.getPositionName());
        }
    }

    /**
     * 保存项目经历
     */
    private void saveProjectHistory(Long userId, List<ThirdPartyParseResultDTO.ProjectExperience> projectList) {
        List<UserProjectHistory> projects = conversionService.convertProjectExperience(userId, projectList);
        for (UserProjectHistory project : projects) {
            projectHistoryMapper.insert(project);
            log.debug("保存项目经历: userId={}, project={}, role={}",
                userId, project.getProjectName(), project.getRole());
        }
    }

    /**
     * 保存技能信息
     */
    private void saveSkills(Long userId, ThirdPartyParseResultDTO.Others others) {
        List<UserSkills> skills = conversionService.convertSkills(userId, others);
        for (UserSkills skill : skills) {
            // 检查是否已存在相同技能
            UserSkills existing = skillsMapper.selectByUserIdAndSkillName(userId, skill.getSkillName());
            if (existing == null) {
                skillsMapper.insert(skill);
                log.debug("保存技能: userId={}, skill={}, type={}",
                    userId, skill.getSkillName(), skill.getSkillType());
            }
        }
    }

    /**
     * 保存解析记录
     */
    private Long saveParseRecord(Long userId, ThirdPartyParseResultDTO parseResult, MultipartFile file) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setUserId(userId);
            record.setOriginalFilename(file.getOriginalFilename());
            record.setFileSize(file.getSize());
            record.setFileType(getFileExtension(file.getOriginalFilename()));
            record.setThirdPartyId(parseResult.getCvId());
            record.setErrorMessage(parseResult.getErrorMessage());
            record.setParseStatus(parseResult.getErrorCode() == 0 ? 2 : 3); // 2-解析成功, 3-解析失败

            // 设置解析结果的JSON字符串 - 保留第三方接口原始数据结构
            try {
                // 将整个第三方解析结果转换为JSON字符串存储，保持原始数据结构
                record.setParseResult(convertThirdPartyResultToJson(parseResult));
            } catch (Exception e) {
                log.warn("转换第三方解析结果为JSON失败: userId={}", userId, e);
                record.setParseResult("{}");
            }

            parseRecordsMapper.insert(record);
            log.debug("保存解析记录成功: userId={}, recordId={}, thirdPartyId={}",
                userId, record.getRecordId(), record.getThirdPartyId());

            return record.getRecordId();

        } catch (Exception e) {
            log.error("保存解析记录失败: userId={}", userId, e);
            throw new RuntimeException("保存解析记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将第三方解析结果转换为JSON字符串（保留原始数据结构）
     */
    private String convertThirdPartyResultToJson(ThirdPartyParseResultDTO parseResult) {
        try {
            // 使用Jackson ObjectMapper将整个第三方解析结果转换为JSON
            // 这样可以保留完整的原始数据结构
            return objectMapper.writeValueAsString(parseResult);
        } catch (Exception e) {
            log.warn("使用ObjectMapper转换第三方解析结果为JSON时发生异常", e);
            // 如果ObjectMapper失败，使用备用方法
            return convertToJsonFallback(parseResult);
        }
    }

    /**
     * 备用的JSON转换方法（简化版本）
     */
    private String convertToJsonFallback(ThirdPartyParseResultDTO parseResult) {
        try {
            StringBuilder json = new StringBuilder();
            json.append("{");

            // 基本信息
            json.append("\"errorCode\":").append(parseResult.getErrorCode()).append(",");
            json.append("\"errorMessage\":\"").append(escapeJson(parseResult.getErrorMessage())).append("\",");
            json.append("\"cvId\":\"").append(escapeJson(parseResult.getCvId())).append("\"");

            // 如果有解析结果，添加解析结果
            if (parseResult.getParsingResult() != null) {
                json.append(",\"parsingResult\":");
                json.append(convertParsingResultToJson(parseResult.getParsingResult()));
            }

            json.append("}");
            return json.toString();

        } catch (Exception e) {
            log.warn("备用JSON转换方法也失败", e);
            return "{\"error\":\"JSON conversion failed\"}";
        }
    }

    /**
     * 转换解析结果部分为JSON
     */
    private String convertParsingResultToJson(ThirdPartyParseResultDTO.ParsingResult parsingResult) {
        StringBuilder json = new StringBuilder();
        json.append("{");

        if (parsingResult.getBasicInfo() != null) {
            ThirdPartyParseResultDTO.BasicInfo basicInfo = parsingResult.getBasicInfo();
            json.append("\"basicInfo\":{");
            json.append("\"name\":\"").append(escapeJson(basicInfo.getName())).append("\",");
            json.append("\"gender\":\"").append(escapeJson(basicInfo.getGender())).append("\",");
            json.append("\"age\":").append(basicInfo.getAge() != null ? basicInfo.getAge() : "null").append(",");
            json.append("\"degree\":\"").append(escapeJson(basicInfo.getDegree())).append("\"");
            json.append("},");
        }

        if (parsingResult.getContactInfo() != null) {
            ThirdPartyParseResultDTO.ContactInfo contactInfo = parsingResult.getContactInfo();
            json.append("\"contactInfo\":{");
            json.append("\"phoneNumber\":\"").append(escapeJson(contactInfo.getPhoneNumber())).append("\",");
            json.append("\"email\":\"").append(escapeJson(contactInfo.getEmail())).append("\"");
            json.append("},");
        }

        // 移除最后的逗号
        if (json.charAt(json.length() - 1) == ',') {
            json.setLength(json.length() - 1);
        }

        json.append("}");
        return json.toString();
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
    }

    /**
     * 构建解析选项JSON
     */
    private String buildParseOptionsJson() {
        return "{\"source\":\"batch_upload\",\"version\":\"1.0\"}";
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "unknown";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }
}
