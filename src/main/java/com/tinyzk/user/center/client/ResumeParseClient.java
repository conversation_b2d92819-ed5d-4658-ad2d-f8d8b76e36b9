package com.tinyzk.user.center.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * 简历解析第三方API客户端
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@FeignClient(
    name = "resume-parse-client",
    url = "${resume.parse.api-url}",
    configuration = ResumeParseClientConfiguration.class
)
public interface ResumeParseClient {

    /**
     * 调用第三方简历解析API
     *
     * @param file 简历文件
     * @param rawtext 是否返回原始文本
     * @param handleImage 是否处理图片
     * @param avatar 是否提取头像
     * @param parseMode 解析模式
     * @param ocrMode OCR模式
     * @param ocrService OCR服务
     * @return 解析结果
     */
    @PostMapping(value = "/parse_file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String parseResume(
            @RequestPart("file") MultipartFile file,
            @RequestParam("rawtext") String rawtext,
            @RequestParam("handle_image") String handleImage,
            @RequestParam("avatar") String avatar,
            @RequestParam("parse_mode") String parseMode,
            @RequestParam("ocr_mode") String ocrMode,
            @RequestParam("ocr_service") String ocrService
    );
}
