## 服务配置
spring:
  application:
    name: user-center
  profiles:
    active: local
  aop:
    proxy-target-class: true
    
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB  # 支持批量上传
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

management:
  server:
    port: 18080
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
  tracing:
    sampling:
      probability: 0.1
  # SkyWalking 配置通过 Agent 参数设置，无需在配置文件中配置

# 缓存预热配置
cache:
  warmup:
    enabled: true
    startup-warmup: true
    schedule:
      enabled: true
      cron: "0 0 2 * * ?"
      before-expire: PT2M
    strategy:
      user-detail-count: 1000
      user-list-pages: 5
      page-size: 20
      hot-data-days: 7
      priorities:
        - userDetail
        - userList
        - clientDetails
        - userMapping
    resource:
      thread-pool-size: 5
      timeout: PT30M
      batch-size: 100
      interval: PT0.1S

# 批量简历上传配置
batch:
  resume:
    upload:
      max-file-count: 20
      max-file-size-mb: 10
      max-total-size-mb: 100
      max-concurrency: 5
      default-timeout-seconds: 60
      max-timeout-seconds: 300
      supported-file-types:
        - pdf
        - doc
        - docx
        - txt
      supported-mime-types:
        - application/pdf
        - application/msword
        - application/vnd.openxmlformats-officedocument.wordprocessingml.document
        - text/plain
      enable-file-type-validation: true
      enable-file-size-validation: true
      enable-virus-scan: false
      temp-file-storage-path: /tmp/batch-resume-upload
      result-retention-hours: 24
      enable-progress-notification: true
      progress-notification-interval: 10
      retry:
        enabled: true
        max-attempts: 3
        retry-interval-ms: 1000
        retryable-exceptions:
          - java.net.SocketTimeoutException
          - java.io.IOException
          - org.springframework.web.client.ResourceAccessException

# 异步任务执行器配置
async:
  task:
    executor:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100
      keep-alive-seconds: 60
      thread-name-prefix: "AsyncTask-"
      wait-for-tasks-to-complete-on-shutdown: true
      await-termination-seconds: 60
      batch-resume-parse-executor:
        core-pool-size: 3
        max-pool-size: 10
        queue-capacity: 50
        keep-alive-seconds: 300
        thread-name-prefix: "BatchResumeParser-"
        wait-for-tasks-to-complete-on-shutdown: true
        await-termination-seconds: 120